/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: zhr
 * Date: 2018-03-28
 */


#ifndef _CFFEX_STAT_MATRIX_H_
#define _CFFEX_STAT_MATRIX_H_

#include <cffex/platform.h>

namespace cffex {
namespace datastruct {

struct status_stat {
    enum { _MAX_STAT_COUNT_ = 128 };
    int  status;

    int  parent_status[_MAX_STAT_COUNT_];
    int  child_status[_MAX_STAT_COUNT_];

    int child_status_count;

    status_stat();
    status_stat(int s);
    void register_child(status_stat &s);
    void register_parent(status_stat &s);
    const char *to_string() const;
};

class stat_matrix {
public:
    bool is_valid_stat_change(int current_status,  int next_status);
    bool is_final_stat(int status);
    void dump() const;

protected:
    void init(int status);

protected:
    status_stat  stat_table_[status_stat::_MAX_STAT_COUNT_];
};


}
}

#endif
