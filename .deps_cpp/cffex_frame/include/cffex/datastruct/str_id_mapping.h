/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: zhr
 * Date: 2019-05-21
 */


#ifndef CFFEX_DATASTRUCT_STR_ID_MAPPING_H
#define CFFEX_DATASTRUCT_STR_ID_MAPPING_H

#include <cffex/datastruct/const_char_pointer_map.h>

namespace cffex {
namespace datastruct {

class str_id_mapping  {
    enum { MAX_COUNT = 0XFFFF};
    enum { ID_NULL   = 0XFFFF };
public:
    str_id_mapping() {
        memset(str_id_, 0, sizeof(str_id_));
    }
protected:
    void register_str_id(uint16_t id, const char *str) {
        msg_ids_.insert(str, id);
        strcpy(str_id_[id], str);
    }
    uint16_t get_id(const char *str) const {
        ID_MAPPING::const_iterator itr = msg_ids_.find(str);
        return (itr == msg_ids_.end()) ? ID_NULL : itr->second;
    }
    const char *get_str(uint16_t id) const {
        return str_id_[id];
    }
private:
    typedef cffex::datastruct::const_char_pointer_map<uint16_t> ID_MAPPING;
    ID_MAPPING  msg_ids_;
    char str_id_[MAX_COUNT][512];
};

}
}

#endif
