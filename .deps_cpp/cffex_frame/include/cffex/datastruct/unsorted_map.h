/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: jinfy
 * Date: 2023-01-09
 */

#ifndef CFFEX_UNSORTED_MAP_H
#define CFFEX_UNSORTED_MAP_H

#include <unordered_map>
#include <list>


namespace cffex {
namespace datastruct {
    
template <class KEY, 
          class VALUE, 
          class HASH = std::hash<KEY>,
          class PRED = std::equal_to<KEY> >
class unsorted_map {
public:
    typedef std::pair<const KEY, VALUE> PAIR_TYPE;
    typedef typename std::list<PAIR_TYPE>::iterator iterator;
    typedef typename std::list<PAIR_TYPE>::const_iterator const_iterator;

public:
    unsorted_map() : size_(0) { }
    ~unsorted_map() { }

    std::pair<iterator, bool> insert(const PAIR_TYPE &new_pair) {
        MAP_ITERATOR itor = key_map_.find(new_pair.first);
        if (itor == key_map_.end()) {
            container_.push_back(new_pair);
            key_map_.insert(std::pair<KEY, iterator>(new_pair.first, --container_.end()));
            size_++;
        } else {
            container_.erase(itor->second);
            container_.push_back(new_pair);
            itor->second = --container_.end();
        }
        return std::pair<iterator, bool>(--container_.end(), true);
    }

    iterator insert(const_iterator c_itor, const PAIR_TYPE& new_pair) {
        MAP_ITERATOR itor = key_map_.find(new_pair.first);
        if (itor != key_map_.end()) {
            container_.erase(itor->second);
            size_--;
        }
        iterator ret_itor = container_.insert(c_itor, new_pair);
        key_map_.insert(std::pair<KEY, iterator>(new_pair.first, ret_itor));
        size_++;
        return ret_itor;
    }

    iterator begin()                { return container_.begin(); }
    const_iterator begin() const    { return container_.begin(); }
    iterator end()                  { return container_.end(); }
    const_iterator end() const      { return container_.end(); }

    iterator find(const KEY &key) {
        if (size_ == 0) {
            return container_.end();
        }
        MAP_ITERATOR itor = key_map_.find(key);
        if (itor == key_map_.end()) {
            return container_.end();
        }
        iterator ret_itor = itor->second;
        return ret_itor;
    }

    const_iterator find(const KEY &key) const {
        if (size_ == 0) {
            return container_.end();
        }
        MAP_ITERATOR itor = key_map_.find(key);
        if (itor == key_map_.end()) {
            return container_.end();
        }
        iterator ret_itor = itor->second;
        return ret_itor;
    }

    void erase(iterator pos) {
        if (pos == container_.end() || size_ == 0) {
            throw std::runtime_error("erase, unexsited iterator!");
        }

        PAIR_TYPE value = *pos;
        container_.erase(pos);
        key_map_.erase(value.first);
        size_--;
    }

    void erase(const KEY &key) {
        iterator itor = find(key);
        if (itor != container_.end()) {
            erase(itor);
        }
    }

    void clear() {
        container_.clear();
        key_map_.clear();
        size_ = 0;
    }

    size_t size()       { return size_; }
    bool empty() const  { return container_.empty(); }


private:
    typedef std::unordered_map<KEY, iterator, HASH, PRED> HASH_MAPPING;
    typedef typename HASH_MAPPING::iterator MAP_ITERATOR;
    typedef typename HASH_MAPPING::const_iterator MAP_CONST_ITERATOR;

    size_t                  size_;
    std::list<PAIR_TYPE>    container_;
    HASH_MAPPING            key_map_;

};

}
}
#endif
