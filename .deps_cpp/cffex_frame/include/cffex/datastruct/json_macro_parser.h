/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: zhr
 * Date: 2023-3-15
 */


#ifndef CFFEX_JSON_MACRO_PARSER_H
#define CFFEX_JSON_MACRO_PARSER_H

#include <string>
#include <cffex/json/json_parser.h>

namespace cffex {
namespace datastruct {

class json_macro_parser {
public:
    json_macro_parser(const char *json_str);
    char        get_char(const char *name);
    int8_t      get_int8_t(const char *name);
    uint8_t     get_uint8_t(const char *name);
    int16_t     get_int16_t(const char *name);
    uint16_t    get_uint16_t(const char *name);
    int32_t     get_int32_t(const char *name);
    uint32_t    get_uint32_t(const char *name);
    int64_t     get_int64_t(const char *name);
    uint64_t    get_uint64_t(const char *name);
    double      get_double(const char *name);
    const char *get_string(const char *name);

private:
    cffex::json::json_decoder             p_;
    cffex::json::json_decoder::node       root_;
};

}
}

#endif
