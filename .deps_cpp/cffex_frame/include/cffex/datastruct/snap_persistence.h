/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: zhr
 * Date: 2023-03-10
 */


#ifndef CFFEX_DATASSTRUCT_SNAP_STORAGE_H
#define CFFEX_DATASSTRUCT_SNAP_STORAGE_H


#include <cffex/system/shm.h>
#include <cffex/filesystem/file.h>
#include <cffex/log/xlog.h>

namespace cffex {
namespace datastruct {

template<typename SNAP_T>
class snap_persistence {
public:
    snap_persistence() { }
    bool open(const char *shm_file) {
        bool is_exist = cffex::filesystem::file::exist(shm_file);
        if(!data_shm_.open_writer(shm_file, sizeof(SNAP_T))) {
            XLOG(XLOG_ERROR, "snap_persistence::%s, open shm[%s] failed\n", __FUNCTION__, shm_file);
            return false;
        }
        shm_data_ = (SNAP_T *)data_shm_.get_buf();
        if (!is_exist) {
            memset(shm_data_, 0, sizeof(SNAP_T));
        }
        return true;
    }
    ~snap_persistence() {
        data_shm_.close();
    }

    void update(const SNAP_T &v) {
        *shm_data_ = v;
        ::msync(shm_data_, sizeof(SNAP_T), MS_ASYNC);
    }

    SNAP_T *get() { return shm_data_; }

private:
    cffex::system::shm          data_shm_;
    SNAP_T                     *shm_data_;
};

}
}


#endif
