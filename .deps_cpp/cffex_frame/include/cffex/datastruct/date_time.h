/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: zhr
 * Date: 2017-06-28
 */


#ifndef CFFEX_COMMON_DATE_TIME_H
#define CFFEX_COMMON_DATE_TIME_H

#include <cffex/platform.h>

namespace cffex {
namespace datastruct {

class date {
public:
    /** date: 20100101 */
    static int days_from_year(const char *date, int begin_year = 1980);

    /** out: return format is YYYYmmdd eg: 20100101
     ** days: days from begin_year
     ** return writed bytes which is always 8 bytes
    */
    static int to_string(char out[16], int days, int begin_year = 1980);

    /** date: 20100101
     ** return weekday(1~7) for date
    */
    static int get_weekday(const char *date);
    static int get_weekday(int days, int begin_year = 1980);


    /** write today to out_time format: YYYYMMDD */
    static void today(char out[16]);
};

class time {
public:
    /** seconds: seconds since 00:00:00
     ** return writed bytes which is always 8 bytes
    */
    static int to_string(int seconds, char out[16]);

    /** time: 10:10:10 */
    static int to_seconds(const char *time);

    /** timestamp: UTC timestamp */
    static int get_day_seconds(time_t timestamp);

    /** return seconds since 00:00:00 */
    static int get_current_day_seconds();


    /** write current datetime to out_time format: HH:mm:ss */
    static void current_time(char out_time[16]);

    /** write current datetime to out_time format: HHmmss */
    static void current_sim_time(char out_time[8]);

    /** return mil sec*/
    static uint64_t get_current_mil_sec();
    /** return macro sec*/
    static uint64_t get_current_macro_sec();
    /** return nano sec*/
    static uint64_t get_current_nano_sec();
};

class date_time {
public:
    /** write current datetime to out_date format:YYYYMMDD
     ** write current datetime to out_time format:HH:mm:ss
    */
    static void current_date_time(char out_date[16], char out_time[16]);

    /** write current datetime to out
     **   format:YYYY-MM-DD HH:mm:ss mil.macro to out eg: 2010-01-01 14:01:01 111.222
     ** return writed bytes witch is always 27
    */
    static int current_date_time(char out[32]);

    /** write current datetime to out
     **   format:YYYYMMDDHHmmss mil.macro to out eg: 20100101140101
     ** return writed bytes witch is always 14
    */
    static int current_sim_date_time(char out[32]);

    /** write http datetime to out
     **   format: Day, Date Mon Year HH:mm:ss GMT to out eg: Mon, 24 Jul 2017 14:00:00 GMT
     ** return writed bytes witch is always 29
    */
    static int current_http_date(char out[32]);

    /** write current UTC datetime to out_date format:YYYYMMDD
     ** write current UTC 2datetime to out_time format:HH:mm:ss
    */
    static void current_UTC_date_time(char out_date[16], char out_time[16]);
};

}
}

#endif
