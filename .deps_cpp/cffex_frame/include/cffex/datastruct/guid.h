/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: zhr
 * Date: 2019-03-07
 */


#ifndef CFFEX_GUID_H
#define CFFEX_GUID_H


#include <cffex/platform.h>
#include <cffex/pattern/singleton.h>

namespace cffex {
namespace datastruct {

class guid_manager  : public cffex::pattern::singleton<guid_manager> {
    friend class cffex::pattern::singleton<guid_manager>;
public:
    ~guid_manager();

    uint64_t create_guid();
    void set_module(uint8_t module);
    void set_guid(uint64_t guid);
    uint64_t get_guid();

protected:
    guid_manager();

private:
    uint8_t module_;
    uint64_t  *guids_;
};

}
}

#define CFFEX_SET_MODULE_GUID(module_id) cffex::datastruct::guid_manager::get_instance()->set_module(module_id)
#define CFFEX_GENERATE_GUID()    cffex::datastruct::guid_manager::get_instance()->create_guid()
#define CFFEX_SET_GUID(guid)     cffex::datastruct::guid_manager::get_instance()->set_guid(guid)
#define CFFEX_GET_GUID()         cffex::datastruct::guid_manager::get_instance()->get_guid()

#endif
