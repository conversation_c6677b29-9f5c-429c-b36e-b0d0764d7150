/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: zhr
 * Date: 2023-09-08
 */


#ifndef CFFEX_DATASSTRUCT_SHM_ARRAY_H
#define CFFEX_DATASSTRUCT_SHM_ARRAY_H


#include <cffex/system/shm.h>
#include <cffex/filesystem/file.h>
#include <cffex/log/xlog.h>

namespace cffex {
namespace datastruct {

template<typename SNAP_T>
class shm_array {
private:
    struct shm_array_head {
        uint32_t    max_count;
        uint32_t    count;
    };
public:
    shm_array(uint32_t max_count = 128) : max_count_(max_count) {

    }

    bool reset(){
        data_shm_.close();
        cffex::filesystem::file::remove(shm_file_.c_str());
        return open(shm_file_.c_str());
    }

    bool open(const char *shm_file) {
        shm_file_ = shm_file;
        bool is_exist = cffex::filesystem::file::exist(shm_file);
        if (is_exist) {
            uint32_t shm_size = cffex::filesystem::file::size(shm_file);
            if (!do_open(shm_size)) {
                return false;
            }
        } else {
            uint32_t shm_size = sizeof(shm_array_head) + max_count_ * sizeof(SNAP_T);
            if (!do_open(shm_size)) {
                return false;
            }
            head_->max_count = max_count_;
            head_->count    = 0;
            memset((char *)shm_data_, 0, shm_size - sizeof(shm_array_head));
        }
        return true;
    }

    ~shm_array() {
        data_shm_.close();
    }

    uint32_t  count() {
        return head_->count;
    }

    uint32_t max_count(){
        return head_->max_count;
    }

    void push_back(const SNAP_T &v) {
        if (head_->count >= head_->max_count) {
            reserve();
        }
        uint32_t index = head_->count;
        shm_data_[index] = v;
        (head_->count)++;
    }

    void insert(uint32_t index, const SNAP_T &v) {
        if (head_->count < index) {
            head_->count = index + 1;
        }
        if (head_->count >= head_->max_count) {
            reserve();
        }
        shm_data_[index] = v;
    }

    void sync() {
        ::msync((char *)head_, shm_size_, MS_ASYNC);
    }

    SNAP_T *at(uint32_t index) {
        return &(shm_data_[index]);
    }

    SNAP_T *first() {
        traverse_index_ = 0;
        if (traverse_index_ >= head_->count) {
            return NULL;
        }
        return &(shm_data_[traverse_index_]);
    }

    SNAP_T *next() {
        traverse_index_++;
        if (traverse_index_ >= head_->count) {
            return NULL;
        }
        return &(shm_data_[traverse_index_]);
    }

private:
    bool reserve() {
        uint32_t  old_size = shm_size_;
        char *old_data = (char *)malloc(old_size);
        memcpy(old_data, shm_data_, old_size);
        max_count_ = head_->max_count * 2;
        uint32_t shm_size = sizeof(shm_array_head) + max_count_ * sizeof(SNAP_T);
        data_shm_.close();
        do_open(shm_size);
        head_->max_count = max_count_;
        memcpy(shm_data_, old_data, old_size);
        free(old_data);
        return true;
    }

    bool do_open(uint32_t  shm_size) {
        shm_size_ = shm_size;
        if(!data_shm_.open_writer(shm_file_.c_str(), shm_size)) {
            XLOG(XLOG_ERROR, "shm_array::%s, open shm[%s] failed\n", __FUNCTION__, shm_file_.c_str());
            return false;
        }
        char *shm_data = (char *)data_shm_.get_buf();
        head_   = (shm_array_head *)shm_data;
        shm_data_ = (SNAP_T *)(shm_data + sizeof(shm_array_head));
        return true;
    }

private:
    std::string                 shm_file_;
    cffex::system::shm          data_shm_;
    shm_array_head              *head_;
    SNAP_T                      *shm_data_;
    int                         max_count_;
    uint32_t                    shm_size_;
    uint32_t                    traverse_index_;
};

}
}


#endif
