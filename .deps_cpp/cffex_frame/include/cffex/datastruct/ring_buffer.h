/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: zhr
 * Date: 2016-05-06
 */


#ifndef CFFEX_ASIO_RING_BUFFER_H
#define CFFEX_ASIO_RING_BUFFER_H

#include <stdlib.h>
#include <cstring>

namespace cffex {
namespace datastruct {

class ring_buffer {
public:
    const static unsigned int BUFFER_INIT_CAPACITY = 10240;
    ring_buffer(unsigned int capacity = BUFFER_INIT_CAPACITY);
    ~ring_buffer();

    void add_capacity(unsigned int size);
    void push_back(const char *p);
    void push_back(const char *p, unsigned int len);

    unsigned int   top(char *buf, unsigned int max_len); /* copy data to buf, return actual copy length */
    char  *head(unsigned int &max_len); /* return pointer to the buffer, max_len means max length can be read in this round */
    unsigned int   pop_front(unsigned int len);

    bool            empty() const ;
    unsigned int    length()  const ;
    unsigned int    capacity() const ;


    void dump();

private:
    char            *base_;
    unsigned int    begin_;
    unsigned int    end_;
    unsigned int    capacity_;
};


}
}
#endif


