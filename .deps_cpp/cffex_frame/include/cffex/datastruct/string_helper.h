/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: zhr
 * Date: 2016-10-14
 */


#ifndef CFFEX_COMMON_STRING_HELPER_H
#define CFFEX_COMMON_STRING_HELPER_H
#include <string>
#include <string.h>
#include <vector>
#include <cffex/platform.h>
#include <cffex/system/cffex_memcpy.h>
#include <cffex/datastruct/slice.h>

namespace cffex {
namespace datastruct {

class string_helper {
public:
    static const char *strstr_comm(const char *str, int len);
    static const char *left_trim(const char *str, int len = 0);
    static const char *right_trim(const char *str, int len = 0);
    static const char *trim(char *str, int len = 0);
    static std::string str_trim(const char *str, int len = 0);
    static std::string str_to_lower(const char *str, int len = 0);
    static const char *get_next_token(char *buffer, char delim, char *&save_ptr);

    static void split(const std::string &line, char delim, std::vector<std::string > *ret);
    static int  split(const char *line, char delim, cffex::datastruct::slice *values, int max_values_count);

    static std::string join(const std::vector<std::string> &vec, const char *delim);
    static std::string &replace(std::string &str, const char *src, const char *dest);

    //static
    inline static int copy(char *dst, const char *src) {
        int len = strlen(src);
        if (0 == len) {
            *dst = 0;
        }else{
            cffex_memcpy(dst, src, len);
        }
        return len;
    }

    static std::string binary_dump_string(const char *buf, int len, int indent = 4);
    static std::string binary_to_string(const char *buf, int len);

    static void bin_to_hex(const void *in, int inlen, char *out);

    static void format(char *out, int *len, int max_len, const char *fmt, ...);
    static std::string format(const char *fmt, ...);
};

}
}

#endif
