/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: zhr
 * Date: 2023-03-15
 */


#ifndef CFFEX_JSON_MACRO_H
#define CFFEX_JSON_MACRO_H

#include <cffex/datastruct/json_macro_parser.h>

#define CFFEX_DATASTRUCT_INIT_FROM_JSON_1(json_str, u_type1, c_type1, name1)                             \
do {                                                                                                     \
    cffex::datastruct::json_macro_parser  p(json_str);                                                   \
    set_##name1((u_type1)(p.get_##c_type1(#name1)));                                                     \
} while(0)


#define CFFEX_DATASTRUCT_INIT_FROM_JSON_2(json_str, u_type1, c_type1, name1,                             \
                                                    u_type2, c_type2, name2)                             \
do {                                                                                                     \
    cffex::datastruct::json_macro_parser  p(json_str);                                                   \
    set_##name1((u_type1)(p.get_##c_type1(#name1)));                                                     \
    set_##name2((u_type1)(p.get_##c_type2(#name2)));                                                     \
} while(0)


#define CFFEX_DATASTRUCT_INIT_FROM_JSON_3(json_str, u_type1, c_type1, name1,                             \
                                                    u_type2, c_type2, name2,                             \
                                                    u_type3, c_type3, name3)                             \
do {                                                                                                     \
    cffex::datastruct::json_macro_parser  p(json_str);                                                   \
    set_##name1((u_type1)(p.get_##c_type1(#name1)));                                                     \
    set_##name2((u_type2)(p.get_##c_type2(#name2)));                                                     \
    set_##name3((u_type3)(p.get_##c_type3(#name3)));                                                     \
} while(0)


#define CFFEX_DATASTRUCT_INIT_FROM_JSON_4(json_str, u_type1, c_type1, name1,                             \
                                                    u_type2, c_type2, name2,                             \
                                                    u_type3, c_type3, name3,                             \
                                                    u_type4, c_type4, name4)                             \
do {                                                                                                     \
    cffex::datastruct::json_macro_parser  p(json_str);                                                   \
    set_##name1((u_type1)(p.get_##c_type1(#name1)));                                                     \
    set_##name2((u_type2)(p.get_##c_type2(#name2)));                                                     \
    set_##name3((u_type3)(p.get_##c_type3(#name3)));                                                     \
    set_##name4((u_type4)(p.get_##c_type4(#name4)));                                                     \
} while(0)


#define CFFEX_DATASTRUCT_INIT_FROM_JSON_5(json_str, u_type1, c_type1, name1,                             \
                                                    u_type2, c_type2, name2,                             \
                                                    u_type3, c_type3, name3,                             \
                                                    u_type4, c_type4, name4,                             \
                                                    u_type5, c_type5, name5)                             \
do {                                                                                                     \
    cffex::datastruct::json_macro_parser  p(json_str);                                                   \
    set_##name1((u_type1)(p.get_##c_type1(#name1)));                                                     \
    set_##name2((u_type2)(p.get_##c_type2(#name2)));                                                     \
    set_##name3((u_type3)(p.get_##c_type3(#name3)));                                                     \
    set_##name4((u_type4)(p.get_##c_type4(#name4)));                                                     \
    set_##name5((u_type5)(p.get_##c_type5(#name5)));                                                     \
} while(0)


#define CFFEX_DATASTRUCT_INIT_FROM_JSON_6(json_str, u_type1, c_type1, name1,                             \
                                                    u_type2, c_type2, name2,                             \
                                                    u_type3, c_type3, name3,                             \
                                                    u_type4, c_type4, name4,                             \
                                                    u_type5, c_type5, name5,                             \
                                                    u_type6, c_type6, name6)                             \
do {                                                                                                     \
    cffex::datastruct::json_macro_parser  p(json_str);                                                   \
    set_##name1((u_type1)(p.get_##c_type1(#name1)));                                                     \
    set_##name2((u_type2)(p.get_##c_type2(#name2)));                                                     \
    set_##name3((u_type3)(p.get_##c_type3(#name3)));                                                     \
    set_##name4((u_type4)(p.get_##c_type4(#name4)));                                                     \
    set_##name5((u_type5)(p.get_##c_type5(#name5)));                                                     \
    set_##name6((u_type6)(p.get_##c_type6(#name6)));                                                     \
} while(0)


#define CFFEX_DATASTRUCT_INIT_FROM_JSON_7(json_str, u_type1, c_type1, name1,                             \
                                                    u_type2, c_type2, name2,                             \
                                                    u_type3, c_type3, name3,                             \
                                                    u_type4, c_type4, name4,                             \
                                                    u_type5, c_type5, name5,                             \
                                                    u_type6, c_type6, name6,                             \
                                                    u_type7, c_type7, name7)                             \
do {                                                                                                     \
    cffex::datastruct::json_macro_parser  p(json_str);                                                   \
    set_##name1((u_type1)(p.get_##c_type1(#name1)));                                                     \
    set_##name2((u_type2)(p.get_##c_type2(#name2)));                                                     \
    set_##name3((u_type3)(p.get_##c_type3(#name3)));                                                     \
    set_##name4((u_type4)(p.get_##c_type4(#name4)));                                                     \
    set_##name5((u_type5)(p.get_##c_type5(#name5)));                                                     \
    set_##name6((u_type6)(p.get_##c_type6(#name6)));                                                     \
    set_##name7((u_type7)(p.get_##c_type7(#name7)));                                                     \
} while(0)


#define CFFEX_DATASTRUCT_INIT_FROM_JSON_8(json_str, u_type1, c_type1, name1,                             \
                                                    u_type2, c_type2, name2,                             \
                                                    u_type3, c_type3, name3,                             \
                                                    u_type4, c_type4, name4,                             \
                                                    u_type5, c_type5, name5,                             \
                                                    u_type6, c_type6, name6,                             \
                                                    u_type7, c_type7, name7,                             \
                                                    u_type8, c_type8, name8)                             \
do {                                                                                                     \
    cffex::datastruct::json_macro_parser  p(json_str);                                                   \
    set_##name1((u_type1)(p.get_##c_type1(#name1)));                                                     \
    set_##name2((u_type2)(p.get_##c_type2(#name2)));                                                     \
    set_##name3((u_type3)(p.get_##c_type3(#name3)));                                                     \
    set_##name4((u_type4)(p.get_##c_type4(#name4)));                                                     \
    set_##name5((u_type5)(p.get_##c_type5(#name5)));                                                     \
    set_##name6((u_type6)(p.get_##c_type6(#name6)));                                                     \
    set_##name7((u_type7)(p.get_##c_type7(#name7)));                                                     \
    set_##name8((u_type8)(p.get_##c_type8(#name8)));                                                     \
} while(0)


#define CFFEX_DATASTRUCT_INIT_FROM_JSON_9(json_str, u_type1, c_type1, name1,                             \
                                                    u_type2, c_type2, name2,                             \
                                                    u_type3, c_type3, name3,                             \
                                                    u_type4, c_type4, name4,                             \
                                                    u_type5, c_type5, name5,                             \
                                                    u_type6, c_type6, name6,                             \
                                                    u_type7, c_type7, name7,                             \
                                                    u_type8, c_type8, name8,                             \
                                                    u_type9, c_type9, name9)                             \
do {                                                                                                     \
    cffex::datastruct::json_macro_parser  p(json_str);                                                   \
    set_##name1((u_type1)(p.get_##c_type1(#name1)));                                                     \
    set_##name2((u_type2)(p.get_##c_type2(#name2)));                                                     \
    set_##name3((u_type3)(p.get_##c_type3(#name3)));                                                     \
    set_##name4((u_type4)(p.get_##c_type4(#name4)));                                                     \
    set_##name5((u_type5)(p.get_##c_type5(#name5)));                                                     \
    set_##name6((u_type6)(p.get_##c_type6(#name6)));                                                     \
    set_##name7((u_type7)(p.get_##c_type7(#name7)));                                                     \
    set_##name8((u_type8)(p.get_##c_type8(#name8)));                                                     \
    set_##name9((u_type9)(p.get_##c_type9(#name9)));                                                     \
} while(0)


#define CFFEX_DATASTRUCT_INIT_FROM_JSON_10(json_str, u_type1, c_type1, name1,                             \
                                                     u_type2, c_type2, name2,                             \
                                                     u_type3, c_type3, name3,                             \
                                                     u_type4, c_type4, name4,                             \
                                                     u_type5, c_type5, name5,                             \
                                                     u_type6, c_type6, name6,                             \
                                                     u_type7, c_type7, name7,                             \
                                                     u_type8, c_type8, name8,                             \
                                                     u_type9, c_type9, name9,                             \
                                                     u_type10, c_type10, name10)                          \
do {                                                                                                      \
    cffex::datastruct::json_macro_parser  p(json_str);                                                    \
    set_##name1((u_type1)(p.get_##c_type1(#name1)));                                                      \
    set_##name2((u_type2)(p.get_##c_type2(#name2)));                                                      \
    set_##name3((u_type3)(p.get_##c_type3(#name3)));                                                      \
    set_##name4((u_type4)(p.get_##c_type4(#name4)));                                                      \
    set_##name5((u_type5)(p.get_##c_type5(#name5)));                                                      \
    set_##name6((u_type6)(p.get_##c_type6(#name6)));                                                      \
    set_##name7((u_type7)(p.get_##c_type7(#name7)));                                                      \
    set_##name8((u_type8)(p.get_##c_type8(#name8)));                                                      \
    set_##name9((u_type9)(p.get_##c_type9(#name9)));                                                      \
    set_##name10((u_type10)(p.get_##c_type10(#name10)));                                                  \
} while(0)


#define CFFEX_DATASTRUCT_INIT_FROM_JSON_11(json_str, u_type1, c_type1, name1,                             \
                                                     u_type2, c_type2, name2,                             \
                                                     u_type3, c_type3, name3,                             \
                                                     u_type4, c_type4, name4,                             \
                                                     u_type5, c_type5, name5,                             \
                                                     u_type6, c_type6, name6,                             \
                                                     u_type7, c_type7, name7,                             \
                                                     u_type8, c_type8, name8,                             \
                                                     u_type9, c_type9, name9,                             \
                                                     u_type10, c_type10, name10,                          \
                                                     u_type11, c_type11, name11)                          \
do {                                                                                                      \
    cffex::datastruct::json_macro_parser  p(json_str);                                                    \
    set_##name1((u_type1)(p.get_##c_type1(#name1)));                                                      \
    set_##name2((u_type2)(p.get_##c_type2(#name2)));                                                      \
    set_##name3((u_type3)(p.get_##c_type3(#name3)));                                                      \
    set_##name4((u_type4)(p.get_##c_type4(#name4)));                                                      \
    set_##name5((u_type5)(p.get_##c_type5(#name5)));                                                      \
    set_##name6((u_type6)(p.get_##c_type6(#name6)));                                                      \
    set_##name7((u_type7)(p.get_##c_type7(#name7)));                                                      \
    set_##name8((u_type8)(p.get_##c_type8(#name8)));                                                      \
    set_##name9((u_type9)(p.get_##c_type9(#name9)));                                                      \
    set_##name10((u_type10)(p.get_##c_type10(#name10)));                                                  \
    set_##name11((u_type11)(p.get_##c_type11(#name11)));                                                  \
} while(0)


#define CFFEX_DATASTRUCT_INIT_FROM_JSON_12(json_str, u_type1, c_type1, name1,                             \
                                                     u_type2, c_type2, name2,                             \
                                                     u_type3, c_type3, name3,                             \
                                                     u_type4, c_type4, name4,                             \
                                                     u_type5, c_type5, name5,                             \
                                                     u_type6, c_type6, name6,                             \
                                                     u_type7, c_type7, name7,                             \
                                                     u_type8, c_type8, name8,                             \
                                                     u_type9, c_type9, name9,                             \
                                                     u_type10, c_type10, name10,                          \
                                                     u_type11, c_type11, name11,                          \
                                                     u_type12, c_type12, name12)                          \
do {                                                                                                      \
    cffex::datastruct::json_macro_parser  p(json_str);                                                    \
    set_##name1((u_type1)(p.get_##c_type1(#name1)));                                                      \
    set_##name2((u_type2)(p.get_##c_type2(#name2)));                                                      \
    set_##name3((u_type3)(p.get_##c_type3(#name3)));                                                      \
    set_##name4((u_type4)(p.get_##c_type4(#name4)));                                                      \
    set_##name5((u_type5)(p.get_##c_type5(#name5)));                                                      \
    set_##name6((u_type6)(p.get_##c_type6(#name6)));                                                      \
    set_##name7((u_type7)(p.get_##c_type7(#name7)));                                                      \
    set_##name8((u_type8)(p.get_##c_type8(#name8)));                                                      \
    set_##name9((u_type9)(p.get_##c_type9(#name9)));                                                      \
    set_##name10((u_type10)(p.get_##c_type10(#name10)));                                                  \
    set_##name11((u_type11)(p.get_##c_type11(#name11)));                                                  \
    set_##name12((u_type12)(p.get_##c_type12(#name12)));                                                  \
} while(0)


#define CFFEX_DATASTRUCT_INIT_FROM_JSON_13(json_str, u_type1, c_type1, name1,                             \
                                                     u_type2, c_type2, name2,                             \
                                                     u_type3, c_type3, name3,                             \
                                                     u_type4, c_type4, name4,                             \
                                                     u_type5, c_type5, name5,                             \
                                                     u_type6, c_type6, name6,                             \
                                                     u_type7, c_type7, name7,                             \
                                                     u_type8, c_type8, name8,                             \
                                                     u_type9, c_type9, name9,                             \
                                                     u_type10, c_type10, name10,                          \
                                                     u_type11, c_type11, name11,                          \
                                                     u_type12, c_type12, name12,                          \
                                                     u_type13, c_type13, name13)                          \
do {                                                                                                      \
    cffex::datastruct::json_macro_parser  p(json_str);                                                    \
    set_##name1((u_type1)(p.get_##c_type1(#name1)));                                                      \
    set_##name2((u_type2)(p.get_##c_type2(#name2)));                                                      \
    set_##name3((u_type3)(p.get_##c_type3(#name3)));                                                      \
    set_##name4((u_type4)(p.get_##c_type4(#name4)));                                                      \
    set_##name5((u_type5)(p.get_##c_type5(#name5)));                                                      \
    set_##name6((u_type6)(p.get_##c_type6(#name6)));                                                      \
    set_##name7((u_type7)(p.get_##c_type7(#name7)));                                                      \
    set_##name8((u_type8)(p.get_##c_type8(#name8)));                                                      \
    set_##name9((u_type9)(p.get_##c_type9(#name9)));                                                      \
    set_##name10((u_type10)(p.get_##c_type10(#name10)));                                                  \
    set_##name11((u_type11)(p.get_##c_type11(#name11)));                                                  \
    set_##name12((u_type12)(p.get_##c_type12(#name12)));                                                  \
    set_##name13((u_type13)(p.get_##c_type13(#name13)));                                                  \
} while(0)


#define CFFEX_DATASTRUCT_INIT_FROM_JSON_14(json_str, u_type1, c_type1, name1,                             \
                                                     u_type2, c_type2, name2,                             \
                                                     u_type3, c_type3, name3,                             \
                                                     u_type4, c_type4, name4,                             \
                                                     u_type5, c_type5, name5,                             \
                                                     u_type6, c_type6, name6,                             \
                                                     u_type7, c_type7, name7,                             \
                                                     u_type8, c_type8, name8,                             \
                                                     u_type9, c_type9, name9,                             \
                                                     u_type10, c_type10, name10,                          \
                                                     u_type11, c_type11, name11,                          \
                                                     u_type12, c_type12, name12,                          \
                                                     u_type13, c_type13, name13,                          \
                                                     u_type14, c_type14, name14)                          \
do {                                                                                                      \
    cffex::datastruct::json_macro_parser  p(json_str);                                                    \
    set_##name1((u_type1)(p.get_##c_type1(#name1)));                                                      \
    set_##name2((u_type2)(p.get_##c_type2(#name2)));                                                      \
    set_##name3((u_type3)(p.get_##c_type3(#name3)));                                                      \
    set_##name4((u_type4)(p.get_##c_type4(#name4)));                                                      \
    set_##name5((u_type5)(p.get_##c_type5(#name5)));                                                      \
    set_##name6((u_type6)(p.get_##c_type6(#name6)));                                                      \
    set_##name7((u_type7)(p.get_##c_type7(#name7)));                                                      \
    set_##name8((u_type8)(p.get_##c_type8(#name8)));                                                      \
    set_##name9((u_type9)(p.get_##c_type9(#name9)));                                                      \
    set_##name10((u_type10)(p.get_##c_type10(#name10)));                                                  \
    set_##name11((u_type11)(p.get_##c_type11(#name11)));                                                  \
    set_##name12((u_type12)(p.get_##c_type12(#name12)));                                                  \
    set_##name13((u_type13)(p.get_##c_type13(#name13)));                                                  \
    set_##name14((u_type14)(p.get_##c_type14(#name14)));                                                  \
} while(0)


#define CFFEX_DATASTRUCT_INIT_FROM_JSON_15(json_str, u_type1, c_type1, name1,                             \
                                                     u_type2, c_type2, name2,                             \
                                                     u_type3, c_type3, name3,                             \
                                                     u_type4, c_type4, name4,                             \
                                                     u_type5, c_type5, name5,                             \
                                                     u_type6, c_type6, name6,                             \
                                                     u_type7, c_type7, name7,                             \
                                                     u_type8, c_type8, name8,                             \
                                                     u_type9, c_type9, name9,                             \
                                                     u_type10, c_type10, name10,                          \
                                                     u_type11, c_type11, name11,                          \
                                                     u_type12, c_type12, name12,                          \
                                                     u_type13, c_type13, name13,                          \
                                                     u_type14, c_type14, name14,                          \
                                                     u_type15, c_type15, name15)                          \
do {                                                                                                      \
    cffex::datastruct::json_macro_parser  p(json_str);                                                    \
    set_##name1((u_type1)(p.get_##c_type1(#name1)));                                                      \
    set_##name2((u_type2)(p.get_##c_type2(#name2)));                                                      \
    set_##name3((u_type3)(p.get_##c_type3(#name3)));                                                      \
    set_##name4((u_type4)(p.get_##c_type4(#name4)));                                                      \
    set_##name5((u_type5)(p.get_##c_type5(#name5)));                                                      \
    set_##name6((u_type6)(p.get_##c_type6(#name6)));                                                      \
    set_##name7((u_type7)(p.get_##c_type7(#name7)));                                                      \
    set_##name8((u_type8)(p.get_##c_type8(#name8)));                                                      \
    set_##name9((u_type9)(p.get_##c_type9(#name9)));                                                      \
    set_##name10((u_type10)(p.get_##c_type10(#name10)));                                                  \
    set_##name11((u_type11)(p.get_##c_type11(#name11)));                                                  \
    set_##name12((u_type12)(p.get_##c_type12(#name12)));                                                  \
    set_##name13((u_type13)(p.get_##c_type13(#name13)));                                                  \
    set_##name14((u_type14)(p.get_##c_type14(#name14)));                                                  \
    set_##name15((u_type15)(p.get_##c_type15(#name15)));                                                  \
} while(0)


#define CFFEX_DATASTRUCT_INIT_FROM_JSON_16(json_str, u_type1, c_type1, name1,                             \
                                                     u_type2, c_type2, name2,                             \
                                                     u_type3, c_type3, name3,                             \
                                                     u_type4, c_type4, name4,                             \
                                                     u_type5, c_type5, name5,                             \
                                                     u_type6, c_type6, name6,                             \
                                                     u_type7, c_type7, name7,                             \
                                                     u_type8, c_type8, name8,                             \
                                                     u_type9, c_type9, name9,                             \
                                                     u_type10, c_type10, name10,                          \
                                                     u_type11, c_type11, name11,                          \
                                                     u_type12, c_type12, name12,                          \
                                                     u_type13, c_type13, name13,                          \
                                                     u_type14, c_type14, name14,                          \
                                                     u_type15, c_type15, name15,                          \
                                                     u_type16, c_type16, name16)                          \
do {                                                                                                      \
    cffex::datastruct::json_macro_parser  p(json_str);                                                    \
    set_##name1((u_type1)(p.get_##c_type1(#name1)));                                                      \
    set_##name2((u_type2)(p.get_##c_type2(#name2)));                                                      \
    set_##name3((u_type3)(p.get_##c_type3(#name3)));                                                      \
    set_##name4((u_type4)(p.get_##c_type4(#name4)));                                                      \
    set_##name5((u_type5)(p.get_##c_type5(#name5)));                                                      \
    set_##name6((u_type6)(p.get_##c_type6(#name6)));                                                      \
    set_##name7((u_type7)(p.get_##c_type7(#name7)));                                                      \
    set_##name8((u_type8)(p.get_##c_type8(#name8)));                                                      \
    set_##name9((u_type9)(p.get_##c_type9(#name9)));                                                      \
    set_##name10((u_type10)(p.get_##c_type10(#name10)));                                                  \
    set_##name11((u_type11)(p.get_##c_type11(#name11)));                                                  \
    set_##name12((u_type12)(p.get_##c_type12(#name12)));                                                  \
    set_##name13((u_type13)(p.get_##c_type13(#name13)));                                                  \
    set_##name14((u_type14)(p.get_##c_type14(#name14)));                                                  \
    set_##name15((u_type15)(p.get_##c_type15(#name15)));                                                  \
    set_##name16((u_type16)(p.get_##c_type16(#name16)));                                                  \
} while(0)


#define CFFEX_DATASTRUCT_INIT_FROM_JSON_17(json_str, u_type1, c_type1, name1,                             \
                                                     u_type2, c_type2, name2,                             \
                                                     u_type3, c_type3, name3,                             \
                                                     u_type4, c_type4, name4,                             \
                                                     u_type5, c_type5, name5,                             \
                                                     u_type6, c_type6, name6,                             \
                                                     u_type7, c_type7, name7,                             \
                                                     u_type8, c_type8, name8,                             \
                                                     u_type9, c_type9, name9,                             \
                                                     u_type10, c_type10, name10,                          \
                                                     u_type11, c_type11, name11,                          \
                                                     u_type12, c_type12, name12,                          \
                                                     u_type13, c_type13, name13,                          \
                                                     u_type14, c_type14, name14,                          \
                                                     u_type15, c_type15, name15,                          \
                                                     u_type16, c_type16, name16,                          \
                                                     u_type17, c_type17, name17)                          \
do {                                                                                                      \
    cffex::datastruct::json_macro_parser  p(json_str);                                                    \
    set_##name1((u_type1)(p.get_##c_type1(#name1)));                                                      \
    set_##name2((u_type2)(p.get_##c_type2(#name2)));                                                      \
    set_##name3((u_type3)(p.get_##c_type3(#name3)));                                                      \
    set_##name4((u_type4)(p.get_##c_type4(#name4)));                                                      \
    set_##name5((u_type5)(p.get_##c_type5(#name5)));                                                      \
    set_##name6((u_type6)(p.get_##c_type6(#name6)));                                                      \
    set_##name7((u_type7)(p.get_##c_type7(#name7)));                                                      \
    set_##name8((u_type8)(p.get_##c_type8(#name8)));                                                      \
    set_##name9((u_type9)(p.get_##c_type9(#name9)));                                                      \
    set_##name10((u_type10)(p.get_##c_type10(#name10)));                                                  \
    set_##name11((u_type11)(p.get_##c_type11(#name11)));                                                  \
    set_##name12((u_type12)(p.get_##c_type12(#name12)));                                                  \
    set_##name13((u_type13)(p.get_##c_type13(#name13)));                                                  \
    set_##name14((u_type14)(p.get_##c_type14(#name14)));                                                  \
    set_##name15((u_type15)(p.get_##c_type15(#name15)));                                                  \
    set_##name16((u_type16)(p.get_##c_type16(#name16)));                                                  \
    set_##name17((u_type17)(p.get_##c_type17(#name17)));                                                  \
} while(0)


#define CFFEX_DATASTRUCT_INIT_FROM_JSON_18(json_str, u_type1, c_type1, name1,                             \
                                                     u_type2, c_type2, name2,                             \
                                                     u_type3, c_type3, name3,                             \
                                                     u_type4, c_type4, name4,                             \
                                                     u_type5, c_type5, name5,                             \
                                                     u_type6, c_type6, name6,                             \
                                                     u_type7, c_type7, name7,                             \
                                                     u_type8, c_type8, name8,                             \
                                                     u_type9, c_type9, name9,                             \
                                                     u_type10, c_type10, name10,                          \
                                                     u_type11, c_type11, name11,                          \
                                                     u_type12, c_type12, name12,                          \
                                                     u_type13, c_type13, name13,                          \
                                                     u_type14, c_type14, name14,                          \
                                                     u_type15, c_type15, name15,                          \
                                                     u_type16, c_type16, name16,                          \
                                                     u_type17, c_type17, name17,                          \
                                                     u_type18, c_type18, name18)                          \
do {                                                                                                      \
    cffex::datastruct::json_macro_parser  p(json_str);                                                    \
    set_##name1((u_type1)(p.get_##c_type1(#name1)));                                                      \
    set_##name2((u_type2)(p.get_##c_type2(#name2)));                                                      \
    set_##name3((u_type3)(p.get_##c_type3(#name3)));                                                      \
    set_##name4((u_type4)(p.get_##c_type4(#name4)));                                                      \
    set_##name5((u_type5)(p.get_##c_type5(#name5)));                                                      \
    set_##name6((u_type6)(p.get_##c_type6(#name6)));                                                      \
    set_##name7((u_type7)(p.get_##c_type7(#name7)));                                                      \
    set_##name8((u_type8)(p.get_##c_type8(#name8)));                                                      \
    set_##name9((u_type9)(p.get_##c_type9(#name9)));                                                      \
    set_##name10((u_type10)(p.get_##c_type10(#name10)));                                                  \
    set_##name11((u_type11)(p.get_##c_type11(#name11)));                                                  \
    set_##name12((u_type12)(p.get_##c_type12(#name12)));                                                  \
    set_##name13((u_type13)(p.get_##c_type13(#name13)));                                                  \
    set_##name14((u_type14)(p.get_##c_type14(#name14)));                                                  \
    set_##name15((u_type15)(p.get_##c_type15(#name15)));                                                  \
    set_##name16((u_type16)(p.get_##c_type16(#name16)));                                                  \
    set_##name17((u_type17)(p.get_##c_type17(#name17)));                                                  \
    set_##name18((u_type18)(p.get_##c_type18(#name18)));                                                  \
} while(0)


#define CFFEX_DATASTRUCT_INIT_FROM_JSON_19(json_str, u_type1, c_type1, name1,                             \
                                                     u_type2, c_type2, name2,                             \
                                                     u_type3, c_type3, name3,                             \
                                                     u_type4, c_type4, name4,                             \
                                                     u_type5, c_type5, name5,                             \
                                                     u_type6, c_type6, name6,                             \
                                                     u_type7, c_type7, name7,                             \
                                                     u_type8, c_type8, name8,                             \
                                                     u_type9, c_type9, name9,                             \
                                                     u_type10, c_type10, name10,                          \
                                                     u_type11, c_type11, name11,                          \
                                                     u_type12, c_type12, name12,                          \
                                                     u_type13, c_type13, name13,                          \
                                                     u_type14, c_type14, name14,                          \
                                                     u_type15, c_type15, name15,                          \
                                                     u_type16, c_type16, name16,                          \
                                                     u_type17, c_type17, name17,                          \
                                                     u_type18, c_type18, name18,                          \
                                                     u_type19, c_type19, name19)                          \
do {                                                                                                      \
    cffex::datastruct::json_macro_parser  p(json_str);                                                    \
    set_##name1((u_type1)(p.get_##c_type1(#name1)));                                                      \
    set_##name2((u_type2)(p.get_##c_type2(#name2)));                                                      \
    set_##name3((u_type3)(p.get_##c_type3(#name3)));                                                      \
    set_##name4((u_type4)(p.get_##c_type4(#name4)));                                                      \
    set_##name5((u_type5)(p.get_##c_type5(#name5)));                                                      \
    set_##name6((u_type6)(p.get_##c_type6(#name6)));                                                      \
    set_##name7((u_type7)(p.get_##c_type7(#name7)));                                                      \
    set_##name8((u_type8)(p.get_##c_type8(#name8)));                                                      \
    set_##name9((u_type9)(p.get_##c_type9(#name9)));                                                      \
    set_##name10((u_type10)(p.get_##c_type10(#name10)));                                                  \
    set_##name11((u_type11)(p.get_##c_type11(#name11)));                                                  \
    set_##name12((u_type12)(p.get_##c_type12(#name12)));                                                  \
    set_##name13((u_type13)(p.get_##c_type13(#name13)));                                                  \
    set_##name14((u_type14)(p.get_##c_type14(#name14)));                                                  \
    set_##name15((u_type15)(p.get_##c_type15(#name15)));                                                  \
    set_##name16((u_type16)(p.get_##c_type16(#name16)));                                                  \
    set_##name17((u_type17)(p.get_##c_type17(#name17)));                                                  \
    set_##name18((u_type18)(p.get_##c_type18(#name18)));                                                  \
    set_##name19((u_type19)(p.get_##c_type19(#name19)));                                                  \
} while(0)


#define CFFEX_DATASTRUCT_INIT_FROM_JSON_20(json_str, u_type1, c_type1, name1,                             \
                                                     u_type2, c_type2, name2,                             \
                                                     u_type3, c_type3, name3,                             \
                                                     u_type4, c_type4, name4,                             \
                                                     u_type5, c_type5, name5,                             \
                                                     u_type6, c_type6, name6,                             \
                                                     u_type7, c_type7, name7,                             \
                                                     u_type8, c_type8, name8,                             \
                                                     u_type9, c_type9, name9,                             \
                                                     u_type10, c_type10, name10,                          \
                                                     u_type11, c_type11, name11,                          \
                                                     u_type12, c_type12, name12,                          \
                                                     u_type13, c_type13, name13,                          \
                                                     u_type14, c_type14, name14,                          \
                                                     u_type15, c_type15, name15,                          \
                                                     u_type16, c_type16, name16,                          \
                                                     u_type17, c_type17, name17,                          \
                                                     u_type18, c_type18, name18,                          \
                                                     u_type19, c_type19, name19,                          \
                                                     u_type20, c_type20, name20)                          \
do {                                                                                                      \
    cffex::datastruct::json_macro_parser  p(json_str);                                                    \
    set_##name1((u_type1)(p.get_##c_type1(#name1)));                                                      \
    set_##name2((u_type2)(p.get_##c_type2(#name2)));                                                      \
    set_##name3((u_type3)(p.get_##c_type3(#name3)));                                                      \
    set_##name4((u_type4)(p.get_##c_type4(#name4)));                                                      \
    set_##name5((u_type5)(p.get_##c_type5(#name5)));                                                      \
    set_##name6((u_type6)(p.get_##c_type6(#name6)));                                                      \
    set_##name7((u_type7)(p.get_##c_type7(#name7)));                                                      \
    set_##name8((u_type8)(p.get_##c_type8(#name8)));                                                      \
    set_##name9((u_type9)(p.get_##c_type9(#name9)));                                                      \
    set_##name10((u_type10)(p.get_##c_type10(#name10)));                                                  \
    set_##name11((u_type11)(p.get_##c_type11(#name11)));                                                  \
    set_##name12((u_type12)(p.get_##c_type12(#name12)));                                                  \
    set_##name13((u_type13)(p.get_##c_type13(#name13)));                                                  \
    set_##name14((u_type14)(p.get_##c_type14(#name14)));                                                  \
    set_##name15((u_type15)(p.get_##c_type15(#name15)));                                                  \
    set_##name16((u_type16)(p.get_##c_type16(#name16)));                                                  \
    set_##name17((u_type17)(p.get_##c_type17(#name17)));                                                  \
    set_##name18((u_type18)(p.get_##c_type18(#name18)));                                                  \
    set_##name19((u_type19)(p.get_##c_type19(#name19)));                                                  \
    set_##name20((u_type20)(p.get_##c_type20(#name20)));                                                  \
} while(0)


#endif
