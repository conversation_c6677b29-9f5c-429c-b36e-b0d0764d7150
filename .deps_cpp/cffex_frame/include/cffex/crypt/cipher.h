/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: zhr
 * Date: 2015-08-10
 */


#ifndef CFFEX_CIPHER_H
#define CFFEX_CIPHER_H

#include <string>
#include <cffex/platform.h>

namespace cffex {
namespace crypt {

class cipher
{
public:
    static void md5(const char *in, char out[16]);
    static void md5(const char *in1, const char *in2, char out[16]);
    static void md5(const char *in1, const char *in2, const char *in3, char out[16]);
    static void md5(const void *in, int len, char out[16]);
    static void md5(const void *in1, int len1, const void *in2, int len2, char out[16]);
    static void md5(const void *in1, int len1, const void *in2, int len2, const void *in3, int len3, char out[16]);
    static std::string md5(const std::string &in);

    static void sha256(const char *in, char out[32]);
    static void sha256(const void *in, int len, char out[32]);
    static std::string sha256(const std::string &in);

    static void sha1(const char *in, char out[20]);
    static void sha1(const void *in, int len, char out[20]);
    static std::string sha1(const std::string &in);

    static int  base64_encode(const char *src, char *out);
    static int  base64_encode(const void *src, int len, char *out);
    static int  base64_decode(const char *src, char *out);
    static int  base64_decode(const void *src, int len, char *out);

    static int  varint_encode(uint64_t in, char *out);
    static void zigzag_encode(int64_t in, uint64_t *out);

    static int  varint_decode(const char *in, uint64_t *out);
    static void zigzag_decode(uint64_t in, int64_t *out);
};

}
}

#endif
