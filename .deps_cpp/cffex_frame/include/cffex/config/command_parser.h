/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: zhr
 * Date: 2018-09-20
 */

#ifndef _CFFEX_COMMAND_PARSER_H_
#define _CFFEX_COMMAND_PARSER_H_
#include <cffex/pattern/function.h>
#include <getopt.h>
#include <map>
#include <string>
#include <vector>

namespace cffex {
namespace config {

class command_parser {
public:
    typedef cffex::function<void(int opt_char, const char *opt_str, const char *opt_arg)> opt_func;

    command_parser() : opt_mark_(0XFF00) {
    }

    void register_opt(const char *opt_str, const char *default_value, const char *comment = NULL);
    void register_opt(int opt_char, const char *opt_str, const char *default_value, const char *comment = NULL);
    void register_opt(int opt_char, const char *opt_str, bool required_arg, const opt_func &f, const char *comment = NULL);

    int parse(int argc, char *argv[]);

    const char *get_opt_argv(const char *opt_str) const;

    void dump() const;
    void usage(const char *exe_name);

private:
    void register_opt(int opt_char, const char *opt_str, bool required_arg, bool optional, const opt_func *f, const char *comment);

private:
    struct opt_info {
        int  required_arg;
        char opt_str[128];
        char comment[256];
    };
    typedef std::map<int, opt_info>    opt_map;
    typedef std::map<int, opt_func>    opt_func_map;
    opt_func_map                       opt_func_;
    opt_map                            opts_;
    int                                opt_mark_;
    std::map<std::string, std::string> opt_values_;
};

} // namespace config
} // namespace cffex

#endif
