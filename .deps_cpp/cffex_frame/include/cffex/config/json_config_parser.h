#ifndef _CFFEX_COMMON_JSON_CONFIG_PARSER_H_
#define _CFFEX_COMMON_JSON_CONFIG_PARSER_H_

#include <cffex/json/json_parser.h>
#include <string>
#include <vector>
#include <map>

namespace cffex {
namespace config {

class json_config_parser {
public:
    typedef cffex::json::json_decoder::node element;
    json_config_parser();
    virtual ~json_config_parser();
    int                  parse_file(const char *config_file);
    int                  parse_buffer(const char *buffer);
    element              get_element(const char *path, element ele = NULL); // search path element in root, if cannot find, return NULL, default is root
    std::string          get_value(const char *path, element ele = NULL);
    std::vector<element> get_array_value(const char *path, element ele = NULL);
    void                 get_child_values(const char *path, std::map<std::string, std::string> *items, element ele = NULL);

private:
    element                   root_;
    cffex::json::json_decoder decoder_;
    char                      tmp_string_value_[1024000];
};

} // namespace config
} // namespace cffex
#endif
