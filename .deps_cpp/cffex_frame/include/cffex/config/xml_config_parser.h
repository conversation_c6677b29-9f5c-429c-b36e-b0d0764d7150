/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: zhr
 * Date: 2015-08-10
 */


#ifndef _CFFEX_COMMON_XML_CONFIG_PARSER_H_
#define _CFFEX_COMMON_XML_CONFIG_PARSER_H_
#include <string>
#include <vector>
#include <map>

namespace cffex {
namespace config {
class xml_config_parser {
public:
    typedef void* element;
    enum { RETURN_KEY = 1 };
    xml_config_parser();
    ~xml_config_parser();
    int parse_file(const char *file);
    int parse_buffer(const char *buffer);
    int parse_detail_buffer(const char *buffer);

    /** if path == NULL return root */
    std::string get_first_child_key(const char *path = NULL, element ele = NULL);

    /** if path == NULL return root */
    std::string get_key(element ele);
    std::string get_attribute(const char *name, const char *path = NULL, element ele = NULL);
    std::string get_parameter(const char *path, element ele = NULL);
    std::string get_parameter(const char *path, const char *defaultvalue, element ele = NULL);
    int get_parameter(const char *path, int defaultvalue, element ele = NULL);
    std::vector<std::string> get_parameters(const char *path, element ele = NULL, int return_key_ = 0);
    std::vector<element> get_elements(const char *path, element ele = NULL);
    element get_element(const char *path, element ele = NULL);
    std::vector<std::string> get_attributes(const char *path, element ele = NULL);
    std::map<std::string, std::string> get_attribute_values(const char *path, element ele = NULL);

    std::vector<element> child_elements(element ele = NULL);

    const std::string& get_error_message()const{return error_;}
    std::string get_string();

private:
    void set_xml_error();
private:
    void            *xmldoc_;
    element         root_;
    std::string     error_;
};

}
}
#endif
