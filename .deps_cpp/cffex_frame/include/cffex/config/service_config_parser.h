/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: zhr
 * Date: 2019-05-21
 */


#ifndef CFFEX_SERVICE_CONFIG_PARSER_H
#define CFFEX_SERVICE_CONFIG_PARSER_H

#include <cffex/config/command_parser.h>
#include <cffex/config/xml_config_parser.h>
#include <cffex/datastruct/const_char_pointer_map.h>

namespace cffex {
namespace config {

class service_config_parser {
 public:
    service_config_parser();
    ~service_config_parser();

    void set_default_config(const char *exe_config, const char *service_config);
    void register_opt(const char *opt_str, const char *default_value, const char *comment = NULL);
    void set_show_version(const cffex::function<void ()> &show_version_f);

    int  parse(const char *exe, int argc, char* argv[]);

    cffex::config::command_parser    *get_command_parser();
    cffex::config::xml_config_parser *get_exe_config();
    cffex::config::xml_config_parser *get_service_config();

    const char *get_opt_argv(const char *opt_str) const;

    void dump() const;

 private:
    void inner_show_help(int opt_char, const char *opt_str, const char *opt_arg);
    void inner_show_version(int opt_char, const char *opt_str, const char *opt_arg);

 private:
    cffex::config::command_parser       parser_;
    cffex::config::xml_config_parser    exe_config_;
    cffex::config::xml_config_parser    service_config_;
    cffex::function<void ()>           *show_version_f_;
    std::string                         exe_name_;
};

}
}

#endif
