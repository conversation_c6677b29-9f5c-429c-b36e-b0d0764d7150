/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: lisc
 * Date: 2020-02-07
 */


#ifndef CFFEX_CSP_CLIENT_H
#define CFFEX_CSP_CLIENT_H

#include <cffex/csp/csp_sub.h>

namespace cffex {
namespace csp {

class csp_client {
public:
    typedef cffex::function<void ( const cffex::net::net_addr & )> CONNECT_EVENT_CALLBACK;

    typedef cffex::function<void (  uint32_t seqno,
                                    const cffex::system::error_code &code,
                                    const char *data,
                                    int len,
                                    bool is_last,
                                    const cffex::net::net_addr &ep,
                                    void *context)>  RSP_CALLBACK;
    typedef cffex::function<void (  const char *data,
                                    int len,
                                    const cffex::net::net_addr &ep)>  DATA_CALLBACK;
    class csp_data_monitor_counter;

public:
    csp_client(cffex::event::io_service_thread *thread, uint16_t nodeid, uint16_t topicid);
    ~csp_client();

    int register_addr(const char *addr);
    void set_connected_callback(const CONNECT_EVENT_CALLBACK &cb); 
    void set_closed_callback(const CONNECT_EVENT_CALLBACK &cb);  
    void set_response_callback(const RSP_CALLBACK &cb);    
    void set_data_callback(const DATA_CALLBACK &cb);           

    int subscribe(int sub_type, const csp_sub::PUB_READ_CALLBACK &callback, int resume_no = 0);
    int request(uint32_t seqno, const char *buf, int len, void *context = NULL);
    int send(const char *buf, int len);

    int dump_monitor_info(char *out, int max_len);

protected:
    void on_connect(const cffex::net::net_addr &addr);
    void on_closed(const cffex::net::net_addr &addr);

    void on_rsp(const cffex::system::error_code &code,
                cffex::net::session_decoder *d,
                const cffex::net::net_addr &ep,
                void *context);

    void on_read(cffex::net::session_decoder *d,
                 const cffex::net::net_addr &ep);

private:
    uint16_t                nodeid_;
    uint16_t                topicid_;

    CONNECT_EVENT_CALLBACK *connect_cb_;
    CONNECT_EVENT_CALLBACK *close_cb_;
    RSP_CALLBACK           *rsp_cb_;
    DATA_CALLBACK          *data_cb_;

    cffex::net::requester  *requester_;
    csp_encoder             en_;

    csp_sub                *sub_;
    csp_data_monitor_counter *counter_;
};


}
}

#endif






