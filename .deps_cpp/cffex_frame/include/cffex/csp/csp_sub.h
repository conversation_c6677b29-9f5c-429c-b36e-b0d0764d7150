/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: lisc
 * Date: 2020-02-07
 */


#ifndef CFFEX_CSP_SUB_H
#define CFFEX_CSP_SUB_H

#include <cffex/net/requester.h>
#include <cffex/csp/csp_parser.h>
#include <cffex/pattern/bind.h>
#include <cffex/event/io_service_thread.h>
#include <cffex/event/thread_timer.h>

namespace cffex {
namespace csp {

class csp_sub {
public:
    typedef cffex::function<void ( const char *data,
                                   int len,
                                   const cffex::net::net_addr &ep)>  PUB_READ_CALLBACK;
public:
    csp_sub(cffex::event::io_service_thread *thread, cffex::net::requester *req, uint16_t nodeid, uint16_t topicid);
    ~csp_sub();

    int  init_subscribe(int sub_type, const PUB_READ_CALLBACK &cb, int resume_no = 0);

    void on_connected();
    void on_closed();
    void on_read(csp_decoder *d, const cffex::net::net_addr &ep);

    uint32_t get_last_pub_package_no() { return last_pub_package_no_; }
    bool is_active() { return is_active_; }

private:
    void do_subscribe();
    void on_sub_ack(csp_decoder *d, const cffex::net::net_addr &ep);
    void on_public(csp_decoder *d, const cffex::net::net_addr &ep);

private:
    cffex::net::requester *sender_;

    uint16_t nodeid_;
    uint16_t topicid_;

    PUB_READ_CALLBACK *cb_;

    csp_encoder en_;
    cffex::event::thread_timer  timer_subscribe_;

    uint32_t  sub_type_;
    uint32_t  last_pub_package_no_;
    bool  can_read_pub_;
    bool  is_active_;
};

}
}

#endif