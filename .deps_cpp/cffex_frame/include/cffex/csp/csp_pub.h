/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: lisc
 * Date: 2020-02-07
 */


#ifndef CFFEX_CSP_PUB_H
#define CFFEX_CSP_PUB_H

#include <cffex/net/responser.h>
#include <cffex/csp/csp_parser.h>
#include <cffex/pattern/bind.h>
#include <cffex/event/io_service_thread.h>
#include <cffex/flowstream/flow.h>
#include <map>

namespace cffex {
namespace csp {

class csp_pub {
    struct sub_node;
    typedef cffex::net::message_node message_node;
public:
    csp_pub(cffex::event::io_service_thread *thread, cffex::net::responser *rsp, uint16_t nodeid, uint16_t topicid);
    ~csp_pub();

    void register_flow(cffex::flowstream::flow *flow);

    void on_closed(message_node &node);
    void on_subscribe(message_node &node, csp_decoder *d);

    int dump_monitor_info(char *out, int max_len);

protected:
    bool do_pub_task();
    void do_publish(uint32_t seqno, const cffex::net::session_node &sess_node, const char *buf, int len);


private:
    uint16_t nodeid_;
    uint16_t topicid_;

    cffex::net::responser *sender_;
    cffex::flowstream::flow *flow_;

    csp_encoder en_;

    typedef std::map<uint64_t, sub_node> SubscriberMap;
    SubscriberMap map_subscriber_;
};

}
}

#endif