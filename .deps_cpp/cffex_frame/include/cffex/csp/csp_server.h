/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: lisc
 * Date: 2020-02-07
 */


#ifndef CFFEX_CSP_SERVER_H
#define CFFEX_CSP_SERVER_H

#include <cffex/csp/csp_pub.h>
//#include <cffex/csp/csp_data_monitor_counter.h>

namespace cffex {
namespace csp {

class inner_flow {
public:
    inner_flow(cffex::flowstream::flow *pub_flow, bool is_inner_flow) : pub_flow_(pub_flow), is_inner_flow_(is_inner_flow) {}
    ~inner_flow() {
        if (is_inner_flow_ && pub_flow_ != NULL) {
            delete pub_flow_;
        }
    }
    cffex::flowstream::flow *get_pub_flow() { return pub_flow_; }

private:
    cffex::flowstream::flow   *pub_flow_;
    bool                       is_inner_flow_;
};

class csp_server {
public:
    typedef cffex::net::message_node message_node;
    typedef cffex::function<void (  message_node &node )> CONNECT_EVENT_CALLBACK;
    typedef cffex::function<void (  message_node &node,
                                    const char *data,
                                    int len)>             READ_CALLBACK;

    class csp_data_monitor_counter;

public:
    csp_server(cffex::event::io_service_thread *thread, uint16_t nodeid, uint16_t topicid);
    ~csp_server();

    int register_addr(const char *addr);

    int init_pub(bool support_multi_thread = false, uint32_t max_node = 102400000);
    int init_pub(const char *flow_dir, const char *flow_name, cffex::event::base_service_thread *async_thread, bool support_multi_thread = false, bool reuse = false, uint32_t max_node = 102400000);
    int init_pub(cffex::flowstream::flow *pub_flow);
    int publish(const char *buf, int len);

    cffex::flowstream::flow *get_pub_flow() { return pub_flow_->get_pub_flow(); }

    void set_request_callback(const READ_CALLBACK &cb);
    void set_data_callback(const READ_CALLBACK &cb);
    void set_connected_callback(const CONNECT_EVENT_CALLBACK &cb);
    void set_closed_callback(const CONNECT_EVENT_CALLBACK &cb);

    int response(message_node &node, const char *buf, int len, bool is_last = true);
    int send(message_node &node, const char *buf, int len);
    int send_to_all(const char *buf, int len);

    int dump_monitor_info(char *out, int max_len);

protected:

    void on_connect(const cffex::net::session_node &sess_node, const cffex::net::net_addr &ep);
    void on_closed(const cffex::net::session_node &sess_node, const cffex::net::net_addr &ep);

    void on_read(const cffex::net::session_node &sess_node,
                 cffex::net::session_decoder *d,
                 const cffex::net::net_addr &ep);


private:
    uint16_t                nodeid_;
    uint16_t                topicid_;
    cffex::event::io_service_thread *thread_;

    CONNECT_EVENT_CALLBACK *connect_cb_;
    CONNECT_EVENT_CALLBACK *close_cb_;
    READ_CALLBACK          *req_cb_;
    READ_CALLBACK          *data_cb_;

    cffex::net::responser  *responser_;
    csp_encoder             en_;

    csp_pub                 *pub_;
    inner_flow              *pub_flow_;

    csp_data_monitor_counter *counter_;
};




}
}

#endif