/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: lisc
 * Date: 2020-02-07
 */


#ifndef CFFEX_CSP_PARSER_H
#define CFFEX_CSP_PARSER_H

#include <cffex/net/parser.h>
#include <cffex/net/net_addr.h>

namespace cffex {
namespace csp {

enum {
    CSP_REQ                 =   0X10,
    CSP_CONT_RSP            =   0X20,
    CSP_LAST_RSP            =   0X21,
    CSP_PUB                 =   0X30,
    CSP_SUB                 =   0X40,
    CSP_SUB_ACK             =   0X41,
    CSP_DATA                =   0X50,
    CSP_HEARTBEAT           =   0X60
};

enum { CSP_SUB_FROM_BEGIN = 0, CSP_SUB_FROM_RESUME = 1, CSP_SUB_FROM_LAST = 0XFFFFFFFF };
enum { CFFEX_CSP_VERSION = 1 };

class csp_decoder : public cffex::net::session_decoder
{
public:
    csp_decoder();
    virtual ~csp_decoder() {}
    static cffex::net::session_decoder *create() { return new csp_decoder; }

    virtual int decode(const char *buf, int len);
    virtual const char *get_buf() const { return buf_; }
    virtual int         get_len() const { return len_; }

    virtual bool        is_heartbeat() const;
    virtual bool        is_request() const;
    virtual bool        is_response() const;
    virtual bool        is_last() const;
    virtual uint32_t    get_seqno()  const;

    uint8_t  get_version() const;
    uint8_t  get_type() const;
    uint16_t get_topicid() const;
    uint16_t get_nodeid() const;

    const char  *get_body()const ;
    uint32_t  get_bodylen()const ;

    void dump() const;
    virtual std::string to_string(bool  in_one_line = true, int indent = 1) const;

private:
    void reset();
private:
    const char *buf_;
    int len_;
};


class csp_encoder : public cffex::net::session_encoder
{
public:
    static cffex::net::session_encoder *create() { return new csp_encoder; }

    virtual void        encode();
    virtual const char *get_buf() const { return buffer_.base(); }
    virtual int         get_len() const { return buffer_.len();  }
    virtual ~csp_encoder() {}

    void init(uint8_t type);

    //void init_req(uint32_t seqno, const char *buf, int len);
    //void init_rsp(uint32_t seqno, const char *buf, int len, bool is_last);

    void set_nodeid(uint16_t reserved);
    void set_topicid(uint16_t reserved);
    void set_seqno(uint32_t seqno);
    void set_body(const void *buf, uint16_t len);

    int type() const { return type_; }

    virtual void heartbeat() { init(CSP_HEARTBEAT); }

private:
    int type_;
};

}
}

#endif
