/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: zhr
 * Date: 2017-12-19
 */


#ifndef CFFEX_MATH_H
#define CFFEX_MATH_H

#include <cmath>

namespace cffex {
namespace algorithm {
class math {
public:
    static float  quick_rsqrt(float number);
    static double normal_pdf(double x);
    static double normal_pdf(double x, double mu, double sigma);
    /** valid in 6 bits */
    static double normal_cdf(double x);

    static bool equal(double v1, double v2);
    static bool less(double v1, double v2);
    static bool less_equal(double v1, double v2);
    static bool greater(double v1, double v2);
    static bool greater_equal(double v1, double v2);
    static double fabs(double v1, double v2);

};

}
}

#endif
