/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: zhr
 * Date: 2017-12-19
 */


#ifndef CFFEX_OPTION_H
#define CFFEX_OPTION_H

#include <cffex/algorithm/math.h>

namespace cffex {

namespace algorithm {
class option {
public:
    /**
        s0 :  current underlying asset
        k  :  exercise price
        r  :  interest rate
        t  :  left time
        sigma : volatility
    */
    static double bs_call_delta(double s0, double k, double r, double t, double vol);
    static double bs_gamma(double s0, double k, double r, double t, double vol);
    static double bs_call_theta(double s0, double k, double r, double t, double vol);
    static double bs_put_theta(double s0, double k, double r, double t, double vol);
    static double bs_call_rho(double s0, double k, double r, double t, double vol);
    static double bs_put_rho(double s0, double k, double r, double t, double vol);

    static double bs_call_price(double s0, double k, double r, double t, double vol);
    static double bs_put_price(double s0, double k, double r, double t, double vol);
    static double bs_vega(double s0, double k, double r, double t, double vol);
    static double newton_iv(double s0, double k, double r, double t,
        double cur_price, double iv = 0.5, double epsilon = 0.0001, int max_times = 10);
    static double binary_iv(double s0, double k, double r, double t,
        double cur_price, double iv = 1, double epsilon = 0.0001, int max_times = 1000);
};

}
}

#endif
