/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: lisc
 * Date: 2021-07-06
 */
#include <cffex/platform.h>

#ifndef _CFFEX_COMPRESS_HELPER_H_
#define _CFFEX_COMPRESS_HELPER_H_

namespace cffex {
namespace algorithm {

class compress_helper {
public:
    /*******************************************************************************
    * Compress gzip data
    * @parm[in_data]  data buffer to be compressed
    * @parm[in_len]   data buffer length
    * @parm[out_data] data buffer to save the compressed datas
    * @parm[out_len]  compressed data buffer length
    * return: true - OK; false - FAIL
    *******************************************************************************/
    static bool gz_compress(const char *in_data, uint64_t in_len, char *out_data, uint64_t *out_len);

    /*******************************************************************************
    * Uncompress gzip data
    * @parm[in_data]  data buffer to be uncompressed
    * @parm[in_len]   data buffer length
    * @parm[out_data] data buffer to save the uncompressed datas
    * @parm[out_len]  uncompressed data buffer length
    * return: true - OK; false - FAIL
    *******************************************************************************/
    static bool gz_uncompress(const char *in_data, uint64_t in_len, char *out_data, uint64_t *out_len);
};


}
}

#endif