/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: zhr
 * Date: 2016-10-21
 */


#ifndef CFFEX_HASH_H
#define CFFEX_HASH_H

namespace cffex {
namespace algorithm {

class hasher {
public:
    static unsigned int    ptrade_hash( unsigned int seed,  unsigned int value);
    static unsigned int    ptrade_hash( unsigned int seed,  double       value);
    static unsigned int    ptrade_hash( unsigned int seed,  const char * value);
    static unsigned int    ptrade_hash( unsigned int seed,  const char * value,  unsigned int len);

	#if defined (USE_BOOST)
    static unsigned int     boost_hash( unsigned int seed,  unsigned int value);
    static unsigned int     boost_hash( unsigned int seed,  double       value);
    static unsigned int     boost_hash( unsigned int seed,  const char * value);
    static unsigned int     boost_hash( unsigned int seed,  const char * value, unsigned int len);
	#endif

    static unsigned int       dek_hash( unsigned int seed,  unsigned int value);
    static unsigned int       dek_hash( unsigned int seed,  double       value);
    static unsigned int       dek_hash( unsigned int seed,  const char * value);
    static unsigned int       dek_hash( unsigned int seed,  const char * value,  unsigned int len);

    static unsigned int       fnv_hash( unsigned int seed,  unsigned int value);
    static unsigned int       fnv_hash( unsigned int seed,  double       value);
    static unsigned int       fnv_hash( unsigned int seed,  const char * value);
    static unsigned int       fnv_hash( unsigned int seed,  const char * value,  unsigned int len);

    static unsigned int     crc32_hash( unsigned int seed,  unsigned int value);
    static unsigned int     crc32_hash( unsigned int seed,  double       value);
    static unsigned int     crc32_hash( unsigned int seed,  const char * value);
    static unsigned int     crc32_hash( unsigned int seed,  const char * value,  unsigned int len);

    #if defined (__SSE4_2__)
    static unsigned int crc32_sse4_hash( unsigned int seed, unsigned int value);
    static unsigned int crc32_sse4_hash( unsigned int seed, double       value);
    static unsigned int crc32_sse4_hash( unsigned int seed, const char * value);
    static unsigned int crc32_sse4_hash( unsigned int seed, const char * value,  unsigned int len);
    #endif

    static unsigned int hash_combile(  unsigned int seed1,  unsigned int seed2);
private:
    static unsigned int dek_hash(    unsigned int seed,  unsigned char c);
};

inline unsigned int hash(unsigned int seed, unsigned int value) {
    #if defined (__SSE4_2__)
        return hasher::crc32_sse4_hash(seed, value);
    #else
        return hasher::ptrade_hash(seed, value);
    #endif
}
inline unsigned int hash(unsigned int seed, const char *value) {
    #if defined (__SSE4_2__)
        return hasher::crc32_sse4_hash(seed, value);
    #else
        return hasher::ptrade_hash(seed, value);
    #endif
}
inline unsigned int hash(unsigned int seed, const char *value, unsigned int len) {
    #if defined (__SSE4_2__)
        return hasher::crc32_sse4_hash(seed, value, len);
    #else
        return hasher::ptrade_hash(seed, value, len);
    #endif
}
inline unsigned int hash(unsigned int seed, double value) {
    #if defined (__SSE4_2__)
        return hasher::crc32_sse4_hash(seed, value);
    #else
        return hasher::ptrade_hash(seed, value);
    #endif
}

}

}

#endif
