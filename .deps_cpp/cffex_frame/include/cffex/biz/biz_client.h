/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: zhr
 * Date: 2018-03-21
 */


#ifndef CFFEX_BIZ_CLIENT_H
#define CFFEX_BIZ_CLIENT_H

#include <cffex/net/net_addr.h>
#include <cffex/biz/biz_parser.h>
#include <cffex/pattern/bind.h>
#include <cffex/system/error_code.h>
#include <cffex/event/io_service_thread.h>
#include <cffex/event/async_log_thread.h>
#include <cffex/flowstream/flow.h>

namespace cffex {
namespace biz {

class biz_client {
public:
    typedef cffex::function<void ( const biz_decoder *d)> BIZ_PUB_DATA_CALLBACK;
    typedef cffex::function<void ( const cffex::system::error_code &ec, uint32_t request_id, const biz_decoder *d, bool is_last, void *context)> BIZ_RSP_CALLBACK;
    typedef cffex::function<void ( const cffex::net::net_addr & )> CONNECT_EVENT_CALLBACK;

    enum { BIZ_SUB_FROM_BEGIN = 0, BIZ_SUB_FROM_LAST = 0XFFFFFFFF };

public:
    biz_client(cffex::event::io_service_thread *thread, uint16_t nodeid);
    ~biz_client();

    /* should be called before start thread */
    int init_topic(uint16_t topicid, const char *addr);
    int set_subscribe_callback(uint16_t topicid, BIZ_PUB_DATA_CALLBACK cb, int sub_type);
    int set_subscribe_flow(uint16_t topicid, cffex::flowstream::flow *flow);  // append to flow, resume subscribe
    int set_subscribe_flow(uint16_t topicid, cffex::flowstream::flow *flow, BIZ_PUB_DATA_CALLBACK cb);
    int set_response_callback(uint16_t topicid, BIZ_RSP_CALLBACK cb);
    int set_data_callback(uint16_t topicid, BIZ_PUB_DATA_CALLBACK cb);
    int set_connected_callback(uint16_t topicid, CONNECT_EVENT_CALLBACK cb);
    int set_closed_callback(uint16_t topicid, CONNECT_EVENT_CALLBACK cb);

    void set_async_log_thread(cffex::event::async_log_thread *log_thread, const char *monitor_log_file, const char *req_rsp_log_file, const char *data_log_file, cffex::memory::mem_pool *mempool = NULL);

    int  request(uint16_t topicid, uint32_t request_id, biz_encoder &en, void *context = NULL);
    int  send(uint16_t topicid, biz_encoder &en);

    template <typename FIELD>
    int  request_field(uint16_t topicid, uint32_t request_id, int msgid, const FIELD &f, void *context = NULL) {
        /* encoder thread-safety */
        biz_encoder *en = get_encoder();
        en->init(msgid);
        en->set_field(f);
        return request(topicid, request_id, *en, context);
    }

    template <typename FIELD>
    int  send_field(uint16_t topicid, int msgid, const FIELD &f) {
        /* encoder thread-safety */
        biz_encoder *en = get_encoder();
        en->init(msgid);
        en->set_field(f);
        return send(topicid, *en);
    }


private:
    biz_encoder* get_encoder();

private:
    class biz_client_impl;
    biz_client_impl *impl_;
    biz_encoder *encoders_[cffex::system::MAX_THREAD_NUM];
};


}
}

#endif
