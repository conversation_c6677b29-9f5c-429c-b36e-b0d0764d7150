/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: jinfy
 * Date: 2023-02-23
 */

#ifndef CFFEX_BIZ_SHM_PUBER_H
#define CFFEX_BIZ_SHM_PUBER_H

#include <cstdint>

#include <cffex/base.h>
#include <cffex/biz/shm/biz_shm_parser.h>
#include <cffex/biz/shm/biz_shm_channel_writer.h>
#include <cffex/event/async_log_thread.h>

namespace cffex {
namespace biz {

// thread unsafe
class biz_shm_puber {
public:
    biz_shm_puber(const char *flow_addr, uint32_t capacity = 4096 * 4096 * 8 /*128M*/, uint32_t block_size = 512);
    ~biz_shm_puber();

    void set_package_head(uint16_t          msgid,
                          biz_shm_msg_type  msg_type    = biz_shm_msg_type::NORMAL,
                          uint64_t          biz_index   = 0,
                          uint64_t          error_id    = 0,
                          uint16_t          src_node_id = 0,
                          uint16_t          dst_node_id = 0,
                          uint64_t          guid        = 0,
                          uint64_t          reserved    = 0,
                          uint64_t          request_id  = 0,
                          bool              is_last     = true);

    void end_pub();

    template <typename FIELD>
    void append_field(const FIELD &f) {
        using field_type = typename FIELD::field_type;
        msg_head field_head{(uint16_t)field_type::ID(), sizeof(field_type)};
        writer_->append(&field_head, sizeof(msg_head));
        writer_->append(static_cast<const field_type *>(&f), sizeof(field_type));
        len_ += sizeof(field_type) + sizeof(msg_head);
    }

    int  dump_monitor_info(char *out, int max_len);

private:
    biz_shm_channel_writer  *writer_{nullptr};
    biz_shm_encoder          en_;
    int                      len_;
};

}  // namespace fb
}  // namespace cffex

#endif