/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: jinfy
 * Date: 2023-02-23
 */

#ifndef CFFEX_BIZ_SHM_RECORD_H
#define CFFEX_BIZ_SHM_RECORD_H

#include <cffex/event.h>

namespace cffex {
namespace biz {

class biz_shm_recorder {
public:
    biz_shm_recorder(cffex::event::event_service_thread *thread);
    ~biz_shm_recorder();

    int  init(uint16_t topic_id, const char *shm_dir, const char *file_dir, const char *topic_name);
    void set_async_log_thread(cffex::event::async_log_thread *log_thread, const char *monitor_log_file, cffex::memory::mem_pool *mempool = NULL);

private:
    class biz_shm_recorder_impl;
    biz_shm_recorder_impl *impl_{nullptr};
};

}
}

#endif