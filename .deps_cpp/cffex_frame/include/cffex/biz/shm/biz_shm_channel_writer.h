/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: jinfy
 * Date: 2023-02-23
 */

#ifndef CFFEX_BIZ_SHM_CHANNEL_WRITER_H
#define CFFEX_BIZ_SHM_CHANNEL_WRITER_H

#include <cstdint>

namespace cffex {
namespace biz {


/** thread unsafe */

class biz_shm_channel_writer {
public:
    biz_shm_channel_writer(uint32_t capacity = 4096*4096*4  /*64M */, uint32_t block_size = 512, uint32_t reserve_page= 4096*8 /*32K*/);
    ~biz_shm_channel_writer();

    int      register_addr(const char *file_path);
    void     append(const void *buf, int len);
    uint32_t get_count();
    uint32_t get_seqno();
    void     increase_total_count(int len);
    int      dump_monitor_info(char *out, int max_len);
    void     dump();

private:
    class biz_shm_channel_writer_impl;
    biz_shm_channel_writer_impl *impl_{nullptr};
};

}
}


#endif