/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: jinfy
 * Date: 2023-02-23
 */

#ifndef CFFEX_BIZ_SHM_SUBER_H
#define CFFEX_BIZ_SHM_SUBER_H

#include <cffex/base.h>
#include <cffex/event.h>
#include <cffex/biz/shm/biz_shm_parser.h>

namespace cffex {
namespace biz {

enum shm_error_code_type { ERROR_PACKAGE = 0, SWITCH_READER, MISS_SEQNO };

using read_callback_type  = cffex::function<bool(const biz_shm_decoder*)>;
using alert_callback_type = cffex::function<void(const shm_error_code_type&, const uint32_t&, void*)>;

class biz_shm_suber {
public:
    biz_shm_suber(const char *flow_addr, cffex::event::base_service_thread *thread, double bandwith_rate = 0.5);
    ~biz_shm_suber();
    void set_under_reader(const char *dir, const char *name);
    void set_expect_seqno(uint32_t expect_seqno);
    void set_read_callback(const read_callback_type &cb);
    void set_alert_callback(const alert_callback_type &cb);
    int  dump_monitor_info(char *out, int max_len);

private:
    class biz_shm_suber_impl;
    biz_shm_suber_impl *impl_{nullptr};
};

}
}


#endif
