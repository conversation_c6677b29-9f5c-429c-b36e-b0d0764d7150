/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: jinfy
 * Date: 2023-02-23
 */

#ifndef CFFEX_BIZ_SHM_PARSER_H
#define CFFEX_BIZ_SHM_PARSER_H

#include <cffex/datastruct/buffer.h>
#include <cffex/datastruct/slice.h>

#include <cstddef>
#include <cstdint>
#include <functional>

namespace cffex {
namespace biz {

enum biz_shm_msg_type : uint8_t { REQ, RSP, NORMAL };
struct biz_shm_head {
    uint8_t             version;
    bool                is_last;
    biz_shm_msg_type    msg_type;  // 0:req 1:rsp 2:normal
    uint16_t            msgid;
    uint16_t            src_node_id;
    uint16_t            dst_node_id;
    uint32_t            seq_no;
    uint64_t            guid;
    uint64_t            timestamp;
    uint64_t            biz_index;
    uint64_t            error_id;
    uint64_t            request_id;
    uint64_t            reserved;
};

struct msg_head {
    uint16_t            fieldid;
    uint16_t            fieldlen;
};

class biz_shm_decoder {
public:
    static biz_shm_decoder *create() {
        return new biz_shm_decoder();
    }

    biz_shm_decoder() : len_(0) {}
    ~biz_shm_decoder() {}

    void     reset();
    int      decode(const char *buf, int len);
    uint16_t get_msgid() const;
    uint32_t get_seqno() const;
    uint64_t get_guid() const;
    uint64_t get_timestamp() const;
    uint64_t get_biz_index() const;
    uint64_t get_reserved() const;
    uint64_t get_error_id() const;
    uint64_t get_request_id() const;
    uint16_t get_src_node_id() const;
    uint16_t get_dst_node_id() const;
    bool     is_req() const;
    bool     is_rsp() const;
    bool     is_last() const;

    const char *get_buf() const {
        return buf_;
    };
    size_t get_len() const {
        return len_;
    };

    template <typename FIELD>
    const FIELD *get_field() const {
        static constexpr int MSG_HEAD_LEN = sizeof(msg_head);
        if (buf_top_ >= buf_end_) {
            return nullptr;
        }

        msg_head *head = (msg_head *)buf_top_;

        if (head->fieldid == FIELD::ID()) {
            const char *ret = buf_top_ + MSG_HEAD_LEN;
            ((biz_shm_decoder *)this)->buf_top_ += (MSG_HEAD_LEN + head->fieldlen);
            return (const FIELD *)ret;
        }
        return nullptr;
    }

    std::string to_string(bool in_one_line = false, int indent = 0) const;

    /** for debug */
    static void set_field_tag(int tag) {
        field_tag_ = tag;
    }

    typedef std::function<const char *(int msgid)> MSGID_HANDLER;
    static void set_msgid_header(MSGID_HANDLER handler) {
        msgid_handler_ = new MSGID_HANDLER(handler);
    }

private:
    uint8_t get_version() const;

private:
    const char *buf_{nullptr};
    int         len_;
    const char *buf_top_{nullptr};
    const char *buf_end_{nullptr};

    /** for debug */
    static int            field_tag_;
    static MSGID_HANDLER *msgid_handler_;
};

class biz_shm_encoder {
public:
    static biz_shm_encoder *create() {
        return new biz_shm_encoder();
    }

    biz_shm_encoder();
    ~biz_shm_encoder() {}

    void set_msgid(uint16_t msgid);
    void set_seqno(uint32_t seqno);
    void set_guid(uint64_t guid);
    void set_timestamp(uint64_t timestamp);
    void set_reserved(uint64_t v);
    void set_biz_index(uint64_t v);
    void set_error_id(uint64_t v);
    void set_request_id(uint64_t v);
    void set_msg_type(biz_shm_msg_type v);
    void set_src_node_id(uint16_t v);
    void set_dst_node_id(uint16_t v);
    void set_is_last(bool v);

    void *get_head() const {
        return (void *)(&head_);
    }
    constexpr int get_head_len() const {
        return sizeof(biz_shm_head);
    }

    /** use in file channel */
    void encode();

    template <typename FIELD>
    void append_field(const FIELD &f) {
        msg_head head{.fieldid  = (uint16_t)FIELD::ID(),
                      .fieldlen = sizeof(FIELD)};
        buffer_.append((const char *)(&head), sizeof(msg_head));
        buffer_.append((const char *)(&f), sizeof(FIELD));
    }

    const char *get_data() const {
        return buffer_.base();
    }
    int get_len() const {
        return buffer_.len();
    }
    /**   */
private:
    biz_shm_head              head_;
    cffex::datastruct::buffer buffer_;  // use in file channel
};

#define CFFEX_SHM_SUB_FIELD_FUNC(FIELD, FUNC, d)           \
    do {                                                   \
        for (auto f = d->get_field<FIELD>(); nullptr != f; \
             f      = d->get_field<FIELD>()) {             \
            FUNC(*f);                                      \
        }                                                  \
    } while (0)

}  // namespace biz
}  // namespace cffex

#endif