/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: zhr
 * Date: 2018-03-21
 */


#ifndef CFFEX_BIZ_SSP_SUB_THREAD_H
#define CFFEX_BIZ_SSP_SUB_THREAD_H

#include <cffex/biz/biz_parser.h>
#include <cffex/ssp/ssp_sub_thread.h>
#include <cffex/pattern/bind.h>
#include <cffex/system/error_code.h>

namespace cffex {
namespace biz {

class biz_ssp_sub_thread : public cffex::ssp::ssp_sub_thread {
    enum { MAX_TOPIC_NUM = 0XFFFF };
public:
    typedef cffex::function<void ( const biz_decoder *d)> BIZ_PUB_READ_CALLBACK;
    typedef cffex::function<void ( const cffex::system::error_code &ec, const biz_decoder *d, void *context)> BIZ_RSP_READ_CALLBACK;

    biz_ssp_sub_thread(uint16_t node_id);
    virtual ~biz_ssp_sub_thread();

    void init_sub(uint16_t topicid, const char *addr, BIZ_PUB_READ_CALLBACK pub_callback, int sub_type =cffex::ssp::ssp_sub_thread::SUB_FROM_BEGIN);
    void init_sub(uint16_t topicid, const char *addr, BIZ_PUB_READ_CALLBACK pub_callback,
        BIZ_RSP_READ_CALLBACK rsp_callback, int sub_type = cffex::ssp::ssp_sub_thread::SUB_FROM_BEGIN);

    int  send_req(uint16_t topicid, biz_encoder *en, void *context = NULL);

protected:
    void on_pub(uint16_t topicid, const char *data, int len);
    void on_rsp(uint16_t topicid, const cffex::system::error_code &ec, const char *data, int len, void *context);

protected:
    biz_decoder decoder_;
    BIZ_PUB_READ_CALLBACK  *pub_cb_[MAX_TOPIC_NUM];
    BIZ_RSP_READ_CALLBACK  *rsp_cb_[MAX_TOPIC_NUM];
};

}
}

#define CFFEX_BIZ_SUB_FIELD_FUNC(FIELD, FUNC, d)                                                                     \
  do {                                                                                                               \
    FIELD f;                                                                                                         \
    cffex::biz::biz_decoder::const_iterator itr = d->begin();                                                        \
    for (; itr != d->end(); ++itr) {                                                                                 \
        if ( 0 != d->get_field(f, itr)) {                                                                            \
            XLOG(XLOG_WARNING, "biz_ssp_sub_thread::%s, get field[%s] failed\n", __FUNCTION__, FIELD::NAME());       \
            continue;                                                                                                \
        }                                                                                                            \
        FUNC(f);                                                                                                     \
    }                                                                                                                \
  } while(0)

#endif
