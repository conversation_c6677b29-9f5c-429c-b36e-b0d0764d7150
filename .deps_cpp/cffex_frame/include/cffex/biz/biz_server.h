/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: zhr
 * Date: 2018-03-21
 */


#ifndef CFFEX_BIZ_SERVER_H
#define CFFEX_BIZ_SERVER_H

#include <cffex/biz/biz_parser.h>
#include <cffex/flowstream/flow.h>
#include <cffex/pattern/bind.h>
#include <cffex/net/net_addr.h>
#include <cffex/event/io_service_thread.h>
#include <cffex/event/async_log_thread.h>

namespace cffex {
namespace biz {

class biz_server {
    enum { MAX_THREAD_NUM = 65536 };
public:
    typedef cffex::net::message_node  message_node;
    typedef cffex::function<void (  message_node &node,
                                    const biz_decoder *d)>      READ_CALLBACK;
    typedef cffex::function<void (  message_node &node )>       CONNECT_EVENT_CALLBACK;
public:
    biz_server(cffex::event::io_service_thread *thread, uint16_t nodeid);
    ~biz_server();

    /* should be called before start thread */
    int init_topic(uint16_t topicid, const char *addr);
    int init_pub_flow(uint16_t topicid, bool support_multi_thread = false, uint32_t max_node = 102400000);
    int init_pub_flow(uint16_t topicid, const char *flow_dir, const char *flow_name, cffex::event::base_service_thread *async_thread, bool support_multi_thread = false, bool reuse = false, uint32_t max_node = 102400000);
    int set_request_callback(uint16_t topicid, READ_CALLBACK cb);
    int set_data_callback(uint16_t topicid, READ_CALLBACK cb);
    int set_connected_callback(uint16_t topicid, CONNECT_EVENT_CALLBACK cb);
    int set_closed_callback(uint16_t topicid, CONNECT_EVENT_CALLBACK cb);

    cffex::flowstream::flow     *get_pub_flow(uint16_t topicid);

    void set_async_log_thread(cffex::event::async_log_thread *log_thread, const char *monitor_log_file, const char *req_rsp_log_file, const char *data_log_file, cffex::memory::mem_pool *mempool = NULL);

    int  publish(uint16_t topicid, biz_encoder &en);
    template <typename FIELD>
    int  publish_field(uint16_t topicid, int msgid, const FIELD &f) {
        /* encoder thread-safety */
        biz_encoder *en = get_encoder();
        en->init(msgid);
        en->set_field(f);
        return publish(topicid, *en);
    }

    int  response(uint16_t topicid, message_node &node, biz_encoder &en, bool is_last = true);
    int  send(uint16_t topicid, message_node &node, biz_encoder &en);
    int  send_to_all(uint16_t topicid, biz_encoder &en);

    template <typename FIELD>
    int  response_field(uint16_t topicid, message_node &node, uint32_t error_code, int msgid, const FIELD &f, bool is_last = true) {
        /* encoder thread-safety */
        biz_encoder *en = get_encoder();
        en->init(msgid, error_code);
        en->set_field(f);
        return response(topicid, node, *en, is_last);
    }

    template <typename FIELD>
    int  send_field(uint16_t topicid, message_node &node, int msgid, const FIELD &f) {
        biz_encoder *en = get_encoder();
        en->init(msgid);
        en->set_field(f);
        return send(topicid, node, *en);
    }

    template <typename FIELD>
    int  send_field_to_all(uint16_t topicid, int msgid, const FIELD &f) {
        biz_encoder *en = get_encoder();
        en->init(msgid);
        en->set_field(f);
        en->check_and_set_guid();
        en->encode();
        return send_to_all(topicid, *en);
    }

private:
    void on_request(uint16_t topicid, message_node &node, const char *data, int len);
    void on_data(uint16_t topicid, message_node &node, const char *data, int len);

private:
    biz_encoder* get_encoder();

private:
    class biz_server_impl;
    biz_server_impl *impl_;
    biz_encoder *encoders_[MAX_THREAD_NUM];
};




}
}

#endif