/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: zhr
 * Date: 2018-03-05
 */


#ifndef _CFFEX_BIZ_PARSER_H_
#define _CFFEX_BIZ_PARSER_H_

#include <cffex/net/field_set.h>
#include <cffex/pattern/function.h>
#include <cffex/datastruct/slice.h>
#include <string>

namespace cffex {
namespace biz {

static const uint8_t BIZ_VERSION = 1;
static const bool BIZ_CONVERT_ENDIAN_DEFAULT = false;

class biz_decoder : public cffex::net::decoder, public cffex::net::field_set_decoder
{
public:
    typedef cffex::function<const char * ( int msgid )> MSGID_HANDLER;

    biz_decoder(bool convert_endian = BIZ_CONVERT_ENDIAN_DEFAULT);
    virtual ~biz_decoder() {}
    int decode(const char *buf, int len);
    virtual const char *get_buf() const { return buf_; }
    virtual int         get_len() const { return len_; }

    uint8_t  get_version() const;
    uint8_t  get_exlen() const;
    uint16_t get_msgid() const;
    uint16_t get_nodeid() const;
    uint32_t get_code()  const;
    uint64_t get_guid() const;

    const cffex::datastruct::slice get_ex_header() const;

    void dump(int indent = 0) const;
    std::string to_string(bool  in_one_line = false, int indent = 0) const;

    static void set_field_tag(int tag) { field_tag_ = tag; }
    static void set_msgid_handler(MSGID_HANDLER handler) {
        msgid_handler_ = new MSGID_HANDLER(handler);
    }

    const char *get_body()      const;
    int         get_body_len()  const;

    void reset();

private:
    static int field_tag_;
    static MSGID_HANDLER *msgid_handler_;
    bool convert_endian_;
    const char *buf_;
    int len_;
    uint8_t exlen_;
};


class biz_encoder : public cffex::net::encoder, public cffex::net::field_set_encoder
{
public:
    biz_encoder(bool convert_endian = BIZ_CONVERT_ENDIAN_DEFAULT);
    biz_encoder(int buf_len, bool convert_endian = BIZ_CONVERT_ENDIAN_DEFAULT);
    virtual ~biz_encoder();
    virtual void        encode();
    virtual const char *get_buf() const { return buffer_.base(); }
    virtual int         get_len() const { return buffer_.len();  }

    void init(uint16_t msgid, uint32_t code = 0);
    void set_ex_header(const char *exheader, uint8_t len);
    void set_nodeid(uint16_t nodeid);
    void set_guid(uint64_t guid);

    void check_and_set_guid();

    /* copy package from biz_decoder,
       after copy, should still call encode(), but do not support set_field */
    int  copy(const biz_decoder *d);

private:
    uint8_t exlen_;
    int     copy_bodylen_;
    bool    convert_endian_;
};


}
}

#endif
