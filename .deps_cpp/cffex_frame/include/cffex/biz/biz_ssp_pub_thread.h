/**
 * CFFEX Confidential.
 *
 * @Copyright 2018 CFFEX.  All rights reserved.
 *
 * The source code for this program is not published or otherwise
 * divested of its trade secrets, irrespective of what has been
 * deposited with the China Copyright Office.
 *
 * Author: zhr
 * Date: 2018-03-21
 */


#ifndef CFFEX_BIZ_SSP_PUB_THREAD_H
#define CFFEX_BIZ_SSP_PUB_THREAD_H

#include <cffex/biz/biz_parser.h>
#include <cffex/ssp/ssp_pub_thread.h>
#include <cffex/flowstream/flow.h>
#include <cffex/pattern/bind.h>
namespace cffex {
namespace biz {

class biz_ssp_pub_thread : public cffex::ssp::ssp_pub_thread {
    enum { MAX_TOPIC_NUM = 0XFFFF };
public:
    typedef cffex::function<void ( uint32_t sessionid, uint32_t seqno, const biz_decoder *d)> BIZ_REQ_READ_CALLBACK;

    biz_ssp_pub_thread(uint16_t node_id);
    virtual ~biz_ssp_pub_thread();

    int  init_pub(uint16_t topicid, const char *addr, bool is_multi_thread = false);
    int  init_pub(uint16_t topicid, const char *addr, BIZ_REQ_READ_CALLBACK callback, bool is_multi_thread = false);

    int  init_pub(uint16_t topicid, const char *addr, cffex::flowstream::flow *flow);
    int  init_pub(uint16_t topicid, const char *addr, cffex::flowstream::flow *flow, BIZ_REQ_READ_CALLBACK callback);

    int  send_rsp(uint16_t topicid, uint32_t sessionid, uint32_t seqno, biz_encoder &en);

    template <typename FIELD>
    void publish_field(uint16_t topicid, int msgid, const FIELD &f) {
        /* encoder thread-safety */
        biz_encoder *en = get_encoder();
        publish_field(*en, topicid, msgid, f);
    }
    template <typename FIELD>
    void publish_field(biz_encoder &en, uint16_t topicid, int msgid, const FIELD &f) {
        /* todo: not need anymore */
        en.init(msgid);
        en.set_field(f);
        do_pub(topicid, &en);
    }

    void begin_publish_field(int msgid) {
        biz_encoder *en = get_encoder();
        en->init(msgid);
    }
    template <typename FIELD>
    void add_publish_field(const FIELD &f) {
        biz_encoder *en = get_encoder();
        en->set_field(f);
    }
    void end_publish_field(uint16_t topicid) {
        biz_encoder *en = get_encoder();
        do_pub(topicid, en);
    }

    void  do_pub(uint16_t topicid, biz_encoder *en);

protected:
    void  on_req(uint16_t topicid, uint32_t sessionid, uint32_t seqno, const char *data, int len);
    biz_encoder *get_encoder();

protected:
    biz_decoder decoder_;
    biz_encoder *encoders_[cffex::system::MAX_THREAD_NUM];
    BIZ_REQ_READ_CALLBACK  *cb_[MAX_TOPIC_NUM];
};

}
}

#endif
